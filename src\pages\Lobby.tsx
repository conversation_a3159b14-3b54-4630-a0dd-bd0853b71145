
"use client";
import { useState, useEffect } from "react";
import { <PERSON>Right, AlertCircle, Settings, LogOut } from "lucide-react";
import { Input } from "@/components/ui/input";
import LobbyButton from "@/components/LobbyButton";
import LeaderboardButton from "@/components/LeaderboardButton";
import { useNavigate } from "react-router-dom";
import BurgerMenu from "@/components/BurgerMenu";
import { useLobbyStore } from "@/store/lobbyStore";
import { useAuthStore } from "@/store/authStore";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import ServerPortConfig from "@/components/ServerPortConfig";
import { getCardImagePath, getCardBackImagePath } from "@/utils/cardUtils";

export default function GameInterface() {
  const [joinCode, setJoinCode] = useState("");
  const [showArrow, setShowArrow] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const navigate = useNavigate();

  // Get state and actions from the auth store
  const { user, logout } = useAuthStore();

  // Get state and actions from the lobby store
  const {
    playerName,
    setPlayerName,
    createLobby,
    joinLobby,
    error
  } = useLobbyStore();

  // Set player name from authenticated user when component mounts
  useEffect(() => {
    if (user) {
      setPlayerName(user.username);
    }
  }, [user, setPlayerName]);

  // Show arrow when join code is not empty
  useEffect(() => {
    setShowArrow(joinCode.length > 0);
  }, [joinCode]);

  // Show error from the store
  useEffect(() => {
    if (error) {
      setErrorMessage(error);
      setIsLoading(false);
    }
  }, [error]);

  // Function to handle creating a lobby
  const handleCreateLobby = async () => {
    if (!playerName) {
      setErrorMessage("Please enter your name");
      return;
    }

    setIsLoading(true);
    setErrorMessage(null);

    try {
      await createLobby();
      navigate("/lobby");
    } catch (error) {
      console.error('Create lobby error:', error);
      if (error instanceof Error && error.message.includes('timeout')) {
        setErrorMessage('Server connection timeout. Please make sure the server is running.');
      } else if (error instanceof Error && error.message.includes('websocket error')) {
        setErrorMessage('Cannot connect to server. Please make sure the server is running at http://localhost:3001');
      } else {
        setErrorMessage(error instanceof Error ? error.message : 'Failed to create lobby');
      }
      setIsLoading(false);
    }
  };

  // Function to handle joining a lobby
  const handleJoinLobby = async () => {
    if (!playerName) {
      setErrorMessage("Please enter your name");
      return;
    }

    if (!joinCode) {
      setErrorMessage("Please enter a lobby code");
      return;
    }

    setIsLoading(true);
    setErrorMessage(null);

    try {
      await joinLobby(joinCode);
      navigate("/lobby"); // Navigate to the lobby page which now handles both hosting and joining
    } catch (error) {
      setErrorMessage(error instanceof Error ? error.message : 'Failed to join lobby');
      setIsLoading(false);
    }
  };

  const HostSVG = () => (
    <div className="w-full h-12 bg-gold/20 flex items-center justify-center text-gold">
      <svg
        width="347"
        height="49"
        viewBox="0 0 347 49"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M5 42.5H4V43.5H5V42.5ZM346.773 42.5L341 36.7265L335.227 42.5L341 48.2735L346.773 42.5ZM4 20V42.5H6V20H4ZM5 43.5H341V41.5H5V43.5Z"
          fill="#E1C760"
        />
        <path
          d="M17.0696 31V7.72727H21.9901V17.3295H31.9787V7.72727H36.8878V31H31.9787V21.3864H21.9901V31H17.0696ZM48.7401 31.3409C46.9749 31.3409 45.4484 30.9659 44.1605 30.2159C42.8802 29.4583 41.8916 28.4053 41.1946 27.0568C40.4976 25.7008 40.1491 24.1288 40.1491 22.3409C40.1491 20.5379 40.4976 18.9621 41.1946 17.6136C41.8916 16.2576 42.8802 15.2045 44.1605 14.4545C45.4484 13.697 46.9749 13.3182 48.7401 13.3182C50.5052 13.3182 52.0279 13.697 53.3082 14.4545C54.5961 15.2045 55.5885 16.2576 56.2855 17.6136C56.9825 18.9621 57.331 20.5379 57.331 22.3409C57.331 24.1288 56.9825 25.7008 56.2855 27.0568C55.5885 28.4053 54.5961 29.4583 53.3082 30.2159C52.0279 30.9659 50.5052 31.3409 48.7401 31.3409ZM48.7628 27.5909C49.5658 27.5909 50.2363 27.3636 50.7741 26.9091C51.312 26.447 51.7173 25.8182 51.9901 25.0227C52.2704 24.2273 52.4105 23.322 52.4105 22.3068C52.4105 21.2917 52.2704 20.3864 51.9901 19.5909C51.7173 18.7955 51.312 18.1667 50.7741 17.7045C50.2363 17.2424 49.5658 17.0114 48.7628 17.0114C47.9522 17.0114 47.2704 17.2424 46.7173 17.7045C46.1719 18.1667 45.759 18.7955 45.4787 19.5909C45.206 20.3864 45.0696 21.2917 45.0696 22.3068C45.0696 23.322 45.206 24.2273 45.4787 25.0227C45.759 25.8182 46.1719 26.447 46.7173 26.9091C47.2704 27.3636 47.9522 27.5909 48.7628 27.5909ZM75.0014 18.5227L70.5696 18.7955C70.4938 18.4167 70.331 18.0758 70.081 17.7727C69.831 17.4621 69.5014 17.2159 69.0923 17.0341C68.6908 16.8447 68.2098 16.75 67.6491 16.75C66.8991 16.75 66.2666 16.9091 65.7514 17.2273C65.2363 17.5379 64.9787 17.9545 64.9787 18.4773C64.9787 18.8939 65.1454 19.2462 65.4787 19.5341C65.812 19.822 66.384 20.053 67.1946 20.2273L70.3537 20.8636C72.0507 21.2121 73.3158 21.7727 74.1491 22.5455C74.9825 23.3182 75.3991 24.3333 75.3991 25.5909C75.3991 26.7348 75.062 27.7386 74.3878 28.6023C73.7211 29.4659 72.8045 30.1402 71.6378 30.625C70.4787 31.1023 69.1416 31.3409 67.6264 31.3409C65.3158 31.3409 63.4749 30.8598 62.1037 29.8977C60.7401 28.928 59.9408 27.6098 59.706 25.9432L64.4673 25.6932C64.6113 26.3977 64.9598 26.9356 65.5128 27.3068C66.0658 27.6705 66.7741 27.8523 67.6378 27.8523C68.4863 27.8523 69.1681 27.6894 69.6832 27.3636C70.206 27.0303 70.4711 26.6023 70.4787 26.0795C70.4711 25.6402 70.2855 25.2803 69.9219 25C69.5582 24.7121 68.9976 24.4924 68.2401 24.3409L65.2173 23.7386C63.5128 23.3977 62.2438 22.8068 61.4105 21.9659C60.5848 21.125 60.1719 20.053 60.1719 18.75C60.1719 17.6288 60.4749 16.6629 61.081 15.8523C61.6946 15.0417 62.5545 14.4167 63.6605 13.9773C64.7741 13.5379 66.0772 13.3182 67.5696 13.3182C69.7741 13.3182 71.509 13.7841 72.7741 14.7159C74.0469 15.6477 74.7893 16.9167 75.0014 18.5227ZM87.7315 13.5455V17.1818H77.2202V13.5455H87.7315ZM79.6065 9.36364H84.4474V25.6364C84.4474 26.0833 84.5156 26.4318 84.652 26.6818C84.7884 26.9242 84.9777 27.0947 85.2202 27.1932C85.4702 27.2917 85.758 27.3409 86.0838 27.3409C86.3111 27.3409 86.5384 27.322 86.7656 27.2841C86.9929 27.2386 87.1671 27.2045 87.2884 27.1818L88.0497 30.7841C87.8073 30.8598 87.4664 30.947 87.027 31.0455C86.5876 31.1515 86.0535 31.2159 85.4247 31.2386C84.258 31.2841 83.2353 31.1288 82.3565 30.7727C81.4853 30.4167 80.8073 29.8636 80.3224 29.1136C79.8376 28.3636 79.599 27.4167 79.6065 26.2727V9.36364Z"
          fill="white"
        />
        <circle cx="5" cy="20" r="5" fill="url(#paint0_linear_762_23)" />
        <defs>
          <linearGradient
            id="paint0_linear_762_23"
            x1="5"
            y1="15"
            x2="5"
            y2="25"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#A07A4A" />
            <stop offset="0.445" stop-color="#EDCF5D" />
            <stop offset="1" stop-color="#7A5A00" />
          </linearGradient>
        </defs>
      </svg>
    </div>
  );

  const PublicSVG = () => (
    <div className="w-full h-12 bg-gold/20 flex items-center justify-center text-gold">
      <svg
        width="347"
        height="49"
        viewBox="0 0 347 49"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M5 42.5H4V43.5H5V42.5ZM346.773 42.5L341 36.7265L335.227 42.5L341 48.2735L346.773 42.5ZM4 20V42.5H6V20H4ZM5 43.5H341V41.5H5V43.5Z"
          fill="#E1C760"
        />
        <path
          d="M16.429 31V7.72727H25.6108C27.3759 7.72727 28.8797 8.06439 30.1222 8.73864C31.3646 9.4053 32.3116 10.3333 32.9631 11.5227C33.6222 12.7045 33.9517 14.0682 33.9517 15.6136C33.9517 17.1591 33.6184 18.5227 32.9517 19.7045C32.285 20.8864 31.3191 21.8068 30.054 22.4659C28.7964 23.125 27.2737 23.4545 25.4858 23.4545H19.6335V19.5114H24.6903C25.6373 19.5114 26.4176 19.3485 27.0312 19.0227C27.6525 18.6894 28.1146 18.2311 28.4176 17.6477C28.7282 17.0568 28.8835 16.3788 28.8835 15.6136C28.8835 14.8409 28.7282 14.1667 28.4176 13.5909C28.1146 13.0076 27.6525 12.5568 27.0312 12.2386C26.41 11.9129 25.6222 11.75 24.6676 11.75H21.3494V31H16.429ZM48.25 23.5682V13.5455H53.0909V31H48.4432V27.8295H48.2614C47.8674 28.8523 47.2121 29.6742 46.2955 30.2955C45.3864 30.9167 44.2765 31.2273 42.9659 31.2273C41.7992 31.2273 40.7727 30.9621 39.8864 30.4318C39 29.9015 38.3068 29.1477 37.8068 28.1705C37.3144 27.1932 37.0644 26.0227 37.0568 24.6591V13.5455H41.8977V23.7955C41.9053 24.8258 42.1818 25.6402 42.7273 26.2386C43.2727 26.8371 44.0038 27.1364 44.9205 27.1364C45.5038 27.1364 46.0492 27.0038 46.5568 26.7386C47.0644 26.4659 47.4735 26.0644 47.7841 25.5341C48.1023 25.0038 48.2576 24.3485 48.25 23.5682ZM57.054 31V7.72727H61.8949V16.4773H62.0426C62.2547 16.0076 62.5616 15.5303 62.9631 15.0455C63.3722 14.553 63.9025 14.1439 64.554 13.8182C65.2131 13.4848 66.0313 13.3182 67.0085 13.3182C68.2813 13.3182 69.4555 13.6515 70.5312 14.3182C71.607 14.9773 72.4669 15.9735 73.1108 17.3068C73.7547 18.6326 74.0767 20.2955 74.0767 22.2955C74.0767 24.2424 73.7623 25.8864 73.1335 27.2273C72.5123 28.5606 71.6638 29.572 70.5881 30.2614C69.5199 30.9432 68.3229 31.2841 66.9972 31.2841C66.0578 31.2841 65.2585 31.1288 64.5994 30.8182C63.9479 30.5076 63.4138 30.1174 62.9972 29.6477C62.5805 29.1705 62.2623 28.6894 62.0426 28.2045H61.8267V31H57.054ZM61.7926 22.2727C61.7926 23.3106 61.9366 24.2159 62.2244 24.9886C62.5123 25.7614 62.929 26.3636 63.4744 26.7955C64.0199 27.2197 64.6828 27.4318 65.4631 27.4318C66.2509 27.4318 66.9176 27.2159 67.4631 26.7841C68.0085 26.3447 68.4214 25.7386 68.7017 24.9659C68.9896 24.1856 69.1335 23.2879 69.1335 22.2727C69.1335 21.2652 68.9934 20.3788 68.7131 19.6136C68.4328 18.8485 68.0199 18.25 67.4744 17.8182C66.929 17.3864 66.2585 17.1705 65.4631 17.1705C64.6752 17.1705 64.0085 17.3788 63.4631 17.7955C62.9252 18.2121 62.5123 18.803 62.2244 19.5682C61.9366 20.3333 61.7926 21.2348 61.7926 22.2727ZM82.1477 7.72727V31H77.3068V7.72727H82.1477ZM86.0256 31V13.5455H90.8665V31H86.0256ZM88.4574 11.2955C87.7377 11.2955 87.1203 11.0568 86.6051 10.5795C86.0975 10.0947 85.8438 9.51515 85.8438 8.84091C85.8438 8.17424 86.0975 7.60227 86.6051 7.125C87.1203 6.64015 87.7377 6.39773 88.4574 6.39773C89.1771 6.39773 89.7907 6.64015 90.2983 7.125C90.8134 7.60227 91.071 8.17424 91.071 8.84091C91.071 9.51515 90.8134 10.0947 90.2983 10.5795C89.7907 11.0568 89.1771 11.2955 88.4574 11.2955ZM102.631 31.3409C100.843 31.3409 99.3049 30.9621 98.017 30.2045C96.7367 29.4394 95.7519 28.3788 95.0625 27.0227C94.3807 25.6667 94.0398 24.1061 94.0398 22.3409C94.0398 20.553 94.3845 18.9848 95.0739 17.6364C95.7708 16.2803 96.7595 15.2235 98.0398 14.4659C99.3201 13.7008 100.843 13.3182 102.608 13.3182C104.131 13.3182 105.464 13.5947 106.608 14.1477C107.752 14.7008 108.657 15.4773 109.324 16.4773C109.991 17.4773 110.358 18.6515 110.426 20H105.858C105.729 19.1288 105.388 18.428 104.835 17.8977C104.29 17.3598 103.574 17.0909 102.688 17.0909C101.938 17.0909 101.282 17.2955 100.722 17.7045C100.169 18.1061 99.7367 18.6932 99.4261 19.4659C99.1155 20.2386 98.9602 21.1742 98.9602 22.2727C98.9602 23.3864 99.1117 24.3333 99.4148 25.1136C99.7254 25.8939 100.161 26.4886 100.722 26.8977C101.282 27.3068 101.938 27.5114 102.688 27.5114C103.241 27.5114 103.737 27.3977 104.176 27.1705C104.623 26.9432 104.991 26.6136 105.278 26.1818C105.574 25.7424 105.767 25.2159 105.858 24.6023H110.426C110.35 25.9356 109.987 27.1098 109.335 28.125C108.691 29.1326 107.801 29.9205 106.665 30.4886C105.528 31.0568 104.184 31.3409 102.631 31.3409Z"
          fill="white"
        />
        <circle cx="5" cy="20" r="5" fill="url(#paint0_linear_762_24)" />
        <defs>
          <linearGradient
            id="paint0_linear_762_24"
            x1="5"
            y1="15"
            x2="5"
            y2="25"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#A07A4A" />
            <stop offset="0.445" stopColor="#EDCF5D" />
            <stop offset="1" stopColor="#7A5A00" />
          </linearGradient>
        </defs>
      </svg>
    </div>
  );

  const PrivateSVG = () => (
    <div className="w-full h-12 bg-gold/20 flex items-center justify-center text-gold">
      <svg
        width="347"
        height="49"
        viewBox="0 0 347 49"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M5 42.5H4V43.5H5V42.5ZM346.773 42.5L341 36.7265L335.227 42.5L341 48.2735L346.773 42.5ZM4 20V42.5H6V20H4ZM5 43.5H341V41.5H5V43.5Z"
          fill="#E1C760"
        />
        <path
          d="M17.1477 31V7.72727H26.3295C28.0947 7.72727 29.5985 8.06439 30.8409 8.73864C32.0833 9.4053 33.0303 10.3333 33.6818 11.5227C34.3409 12.7045 34.6705 14.0682 34.6705 15.6136C34.6705 17.1591 34.3371 18.5227 33.6705 19.7045C33.0038 20.8864 32.0379 21.8068 30.7727 22.4659C29.5152 23.125 27.9924 23.4545 26.2045 23.4545H20.3523V19.5114H25.4091C26.3561 19.5114 27.1364 19.3485 27.75 19.0227C28.3712 18.6894 28.8333 18.2311 29.1364 17.6477C29.447 17.0568 29.6023 16.3788 29.6023 15.6136C29.6023 14.8409 29.447 14.1667 29.1364 13.5909C28.8333 13.0076 28.3712 12.5568 27.75 12.2386C27.1288 11.9129 26.3409 11.75 25.3864 11.75H22.0682V31H17.1477ZM37.7756 31V13.5455H42.4688V16.5909H42.6506C42.9688 15.5076 43.5028 14.6894 44.2528 14.1364C45.0028 13.5758 45.8665 13.2955 46.8438 13.2955C47.0862 13.2955 47.3475 13.3106 47.6278 13.3409C47.9081 13.3712 48.1544 13.4129 48.3665 13.4659V17.7614C48.1392 17.6932 47.8248 17.6326 47.4233 17.5795C47.0218 17.5265 46.6544 17.5 46.321 17.5C45.6089 17.5 44.9725 17.6553 44.4119 17.9659C43.8589 18.2689 43.4195 18.6932 43.0938 19.2386C42.7756 19.7841 42.6165 20.4129 42.6165 21.125V31H37.7756ZM50.8693 31V13.5455H55.7102V31H50.8693ZM53.3011 11.2955C52.5814 11.2955 51.964 11.0568 51.4489 10.5795C50.9413 10.0947 50.6875 9.51515 50.6875 8.84091C50.6875 8.17424 50.9413 7.60227 51.4489 7.125C51.964 6.64015 52.5814 6.39773 53.3011 6.39773C54.0208 6.39773 54.6345 6.64015 55.142 7.125C55.6572 7.60227 55.9148 8.17424 55.9148 8.84091C55.9148 9.51515 55.6572 10.0947 55.142 10.5795C54.6345 11.0568 54.0208 11.2955 53.3011 11.2955ZM75.8608 13.5455L69.7585 31H64.304L58.2017 13.5455H63.3153L66.9403 26.0341H67.1222L70.7358 13.5455H75.8608ZM82.8466 31.3295C81.733 31.3295 80.7405 31.1364 79.8693 30.75C78.9981 30.3561 78.3087 29.7765 77.8011 29.0114C77.3011 28.2386 77.0511 27.2765 77.0511 26.125C77.0511 25.1553 77.2292 24.3409 77.5852 23.6818C77.9413 23.0227 78.4261 22.4924 79.0398 22.0909C79.6534 21.6894 80.3504 21.3864 81.1307 21.1818C81.9186 20.9773 82.7443 20.8333 83.608 20.75C84.6231 20.6439 85.4413 20.5455 86.0625 20.4545C86.6837 20.3561 87.1345 20.2121 87.4148 20.0227C87.6951 19.8333 87.8352 19.553 87.8352 19.1818V19.1136C87.8352 18.3939 87.608 17.8371 87.1534 17.4432C86.7064 17.0492 86.0701 16.8523 85.2443 16.8523C84.3731 16.8523 83.6799 17.0455 83.1648 17.4318C82.6496 17.8106 82.3087 18.2879 82.142 18.8636L77.6648 18.5C77.892 17.4394 78.339 16.5227 79.0057 15.75C79.6723 14.9697 80.5322 14.3712 81.5852 13.9545C82.6458 13.5303 83.8731 13.3182 85.267 13.3182C86.2367 13.3182 87.1648 13.4318 88.0511 13.6591C88.9451 13.8864 89.7367 14.2386 90.4261 14.7159C91.1231 15.1932 91.6723 15.8068 92.0739 16.5568C92.4754 17.2992 92.6761 18.1894 92.6761 19.2273V31H88.0852V28.5795H87.9489C87.6686 29.125 87.2936 29.6061 86.8239 30.0227C86.3542 30.4318 85.7898 30.7538 85.1307 30.9886C84.4716 31.2159 83.7102 31.3295 82.8466 31.3295ZM84.233 27.9886C84.9451 27.9886 85.5739 27.8485 86.1193 27.5682C86.6648 27.2803 87.0928 26.8939 87.4034 26.4091C87.714 25.9242 87.8693 25.375 87.8693 24.7614V22.9091C87.7178 23.0076 87.5095 23.0985 87.2443 23.1818C86.9867 23.2576 86.6951 23.3295 86.3693 23.3977C86.0436 23.4583 85.7178 23.5152 85.392 23.5682C85.0663 23.6136 84.7708 23.6553 84.5057 23.6932C83.9375 23.7765 83.4413 23.9091 83.017 24.0909C82.5928 24.2727 82.2633 24.5189 82.0284 24.8295C81.7936 25.1326 81.6761 25.5114 81.6761 25.9659C81.6761 26.625 81.9148 27.1288 82.392 27.4773C82.8769 27.8182 83.4905 27.9886 84.233 27.9886ZM105.716 13.5455V17.1818H95.2045V13.5455H105.716ZM97.5909 9.36364H102.432V25.6364C102.432 26.0833 102.5 26.4318 102.636 26.6818C102.773 26.9242 102.962 27.0947 103.205 27.1932C103.455 27.2917 103.742 27.3409 104.068 27.3409C104.295 27.3409 104.523 27.322 104.75 27.2841C104.977 27.2386 105.152 27.2045 105.273 27.1818L106.034 30.7841C105.792 30.8598 105.451 30.947 105.011 31.0455C104.572 31.1515 104.038 31.2159 103.409 31.2386C102.242 31.2841 101.22 31.1288 100.341 30.7727C99.4697 30.4167 98.7917 29.8636 98.3068 29.1136C97.822 28.3636 97.5833 27.4167 97.5909 26.2727V9.36364ZM116.636 31.3409C114.841 31.3409 113.295 30.9773 112 30.25C110.712 29.5152 109.72 28.4773 109.023 27.1364C108.326 25.7879 107.977 24.1932 107.977 22.3523C107.977 20.5568 108.326 18.9811 109.023 17.625C109.72 16.2689 110.701 15.2121 111.966 14.4545C113.239 13.697 114.731 13.3182 116.443 13.3182C117.595 13.3182 118.667 13.5038 119.659 13.875C120.659 14.2386 121.53 14.7879 122.273 15.5227C123.023 16.2576 123.606 17.1818 124.023 18.2955C124.439 19.4015 124.648 20.697 124.648 22.1818V23.5114H109.909V20.5114H120.091C120.091 19.8144 119.939 19.197 119.636 18.6591C119.333 18.1212 118.913 17.7008 118.375 17.3977C117.845 17.0871 117.227 16.9318 116.523 16.9318C115.788 16.9318 115.136 17.1023 114.568 17.4432C114.008 17.7765 113.568 18.2273 113.25 18.7955C112.932 19.3561 112.769 19.9811 112.761 20.6705V23.5227C112.761 24.3864 112.92 25.1326 113.239 25.7614C113.564 26.3902 114.023 26.875 114.614 27.2159C115.205 27.5568 115.905 27.7273 116.716 27.7273C117.254 27.7273 117.746 27.6515 118.193 27.5C118.64 27.3485 119.023 27.1212 119.341 26.8182C119.659 26.5152 119.902 26.1439 120.068 25.7045L124.545 26C124.318 27.0758 123.852 28.0152 123.148 28.8182C122.451 29.6136 121.549 30.2348 120.443 30.6818C119.345 31.1212 118.076 31.3409 116.636 31.3409Z"
          fill="white"
        />
        <circle cx="5" cy="19" r="5" fill="url(#paint0_linear_762_25)" />
        <defs>
          <linearGradient
            id="paint0_linear_762_25"
            x1="5"
            y1="14"
            x2="5"
            y2="24"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#A07A4A" />
            <stop offset="0.445" stopColor="#EDCF5D" />
            <stop offset="1" stopColor="#7A5A00" />
          </linearGradient>
        </defs>
      </svg>
    </div>
  );

  return (
    <div className="min-h-screen bg-dark text-white flex flex-col relative">
      {/* BurgerMenu - positioned to not block interactions */}
      <BurgerMenu />

      {/* Server Configuration */}
      <ServerPortConfig />

      {/* Main Content - with proper z-index to ensure clickability */}
      <div className="flex flex-col items-center px-6 pb-16 space-y-6 mt-16 overflow-y-auto h-[calc(100vh-4rem)] z-10 relative">
        {/* Title */}
        <svg
          width="400"
          height="88"
          viewBox="0 0 285 88"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="z-10"
        >
          <path
            d="M59.5916 24.36H85.9916V32.616H84.6476L83.1116 27.288C83.0156 26.872 82.9036 26.584 82.7756 26.424C82.6796 26.232 82.4716 26.12 82.1516 26.088C81.8316 26.056 81.2876 26.04 80.5196 26.04H75.6716V52.968C75.6716 54.056 75.7196 54.712 75.8156 54.936C75.9116 55.128 76.2156 55.256 76.7276 55.32L79.3676 55.656V57H66.2156V55.656L68.8556 55.32C69.3676 55.256 69.6716 55.128 69.7676 54.936C69.8636 54.712 69.9116 54.056 69.9116 52.968V26.04H65.0636C64.3276 26.04 63.7836 26.056 63.4316 26.088C63.1116 26.12 62.9036 26.232 62.8076 26.424C62.7116 26.584 62.5996 26.872 62.4716 27.288L60.9356 32.616H59.5916V24.36ZM115.618 55.656V57H102.706V55.656L105.346 55.32C105.858 55.256 106.162 55.128 106.258 54.936C106.354 54.712 106.402 54.056 106.402 52.968V40.872C106.402 38.664 106.066 37.064 105.394 36.072C104.754 35.048 103.794 34.536 102.514 34.536C101.97 34.536 101.33 34.68 100.594 34.968C99.8901 35.224 99.1701 35.704 98.4341 36.408C97.7301 37.112 97.1061 38.152 96.5621 39.528V52.968C96.5621 54.056 96.6101 54.712 96.7061 54.936C96.8021 55.128 97.1061 55.256 97.6181 55.32L100.258 55.656V57H87.3461V55.656L89.9861 55.32C90.4981 55.256 90.8021 55.128 90.8981 54.936C90.9941 54.712 91.0421 54.056 91.0421 52.968V25.512C91.0421 24.424 90.9941 23.784 90.8981 23.592C90.8021 23.368 90.4981 23.224 89.9861 23.16L87.3461 22.824V21.48H96.5621V36.408C97.4901 34.936 98.6421 33.864 100.018 33.192C101.426 32.52 102.834 32.184 104.242 32.184C106.61 32.184 108.482 32.936 109.858 34.44C111.234 35.944 111.922 38.072 111.922 40.824V52.968C111.922 54.056 111.97 54.712 112.066 54.936C112.162 55.128 112.466 55.256 112.978 55.32L115.618 55.656ZM144.476 55.656V57H135.548V52.776C134.652 54.408 133.5 55.608 132.092 56.376C130.684 57.112 129.26 57.48 127.82 57.48C125.548 57.48 123.74 56.728 122.396 55.224C121.052 53.688 120.38 51.56 120.38 48.84V36.696C120.38 35.608 120.332 34.968 120.236 34.776C120.14 34.552 119.836 34.408 119.324 34.344L116.684 34.008V32.664H125.9V48.792C125.9 51 126.204 52.616 126.812 53.64C127.452 54.632 128.364 55.128 129.548 55.128C130.06 55.128 130.652 55 131.324 54.744C132.028 54.456 132.732 53.96 133.436 53.256C134.14 52.52 134.748 51.48 135.26 50.136V36.696C135.26 35.608 135.212 34.952 135.116 34.728C135.02 34.504 134.716 34.376 134.204 34.344L130.604 34.008V32.664H140.78V52.968C140.78 54.056 140.828 54.712 140.924 54.936C141.02 55.128 141.324 55.256 141.836 55.32L144.476 55.656ZM174.642 55.656V57H161.73V55.656L164.37 55.32C164.882 55.256 165.186 55.128 165.282 54.936C165.378 54.712 165.426 54.056 165.426 52.968V40.872C165.426 38.664 165.09 37.064 164.418 36.072C163.778 35.048 162.818 34.536 161.538 34.536C160.994 34.536 160.354 34.68 159.618 34.968C158.914 35.224 158.194 35.704 157.458 36.408C156.754 37.112 156.13 38.152 155.586 39.528V52.968C155.586 54.056 155.634 54.712 155.73 54.936C155.826 55.128 156.13 55.256 156.642 55.32L159.282 55.656V57H146.37V55.656L149.01 55.32C149.522 55.256 149.826 55.128 149.922 54.936C150.018 54.712 150.066 54.056 150.066 52.968V36.696C150.066 35.608 150.018 34.968 149.922 34.776C149.826 34.552 149.522 34.408 149.01 34.344L146.37 34.008V32.664H155.298V36.888C156.226 35.256 157.41 34.072 158.85 33.336C160.322 32.568 161.794 32.184 163.266 32.184C165.634 32.184 167.506 32.936 168.882 34.44C170.258 35.944 170.946 38.072 170.946 40.824V52.968C170.946 54.056 170.994 54.712 171.09 54.936C171.186 55.128 171.49 55.256 172.002 55.32L174.642 55.656ZM197.924 51.288C197.348 53.528 196.276 55.128 194.708 56.088C193.14 57.016 191.316 57.48 189.236 57.48C185.396 57.48 182.516 56.376 180.596 54.168C178.676 51.928 177.716 48.968 177.716 45.288V44.328C177.716 40.648 178.612 37.704 180.404 35.496C182.228 33.288 184.852 32.184 188.276 32.184C190.196 32.184 191.892 32.568 193.364 33.336C194.868 34.072 196.036 35.144 196.868 36.552C197.732 37.96 198.164 39.672 198.164 41.688C198.164 42.168 198.132 42.68 198.068 43.224C198.036 43.768 197.956 44.312 197.828 44.856H183.62V46.248C183.62 49.64 184.1 52.088 185.06 53.592C186.02 55.064 187.572 55.8 189.716 55.8C191.092 55.8 192.26 55.4 193.22 54.6C194.18 53.768 194.724 52.376 194.852 50.424L197.924 51.288ZM188.276 33.864C186.708 33.864 185.54 34.584 184.772 36.024C184.004 37.464 183.62 39.848 183.62 43.176H192.308C192.436 42.536 192.516 41.944 192.548 41.4C192.612 40.824 192.644 40.296 192.644 39.816C192.644 37.768 192.244 36.264 191.444 35.304C190.644 34.344 189.588 33.864 188.276 33.864ZM223.33 51.288C222.754 53.528 221.682 55.128 220.114 56.088C218.546 57.016 216.722 57.48 214.642 57.48C210.802 57.48 207.922 56.376 206.002 54.168C204.082 51.928 203.122 48.968 203.122 45.288V44.328C203.122 40.648 204.018 37.704 205.81 35.496C207.634 33.288 210.258 32.184 213.682 32.184C215.602 32.184 217.298 32.568 218.77 33.336C220.274 34.072 221.442 35.144 222.274 36.552C223.138 37.96 223.57 39.672 223.57 41.688C223.57 42.168 223.538 42.68 223.474 43.224C223.442 43.768 223.362 44.312 223.234 44.856H209.026V46.248C209.026 49.64 209.506 52.088 210.466 53.592C211.426 55.064 212.978 55.8 215.122 55.8C216.498 55.8 217.666 55.4 218.626 54.6C219.586 53.768 220.13 52.376 220.258 50.424L223.33 51.288ZM213.682 33.864C212.114 33.864 210.946 34.584 210.178 36.024C209.41 37.464 209.026 39.848 209.026 43.176H217.714C217.842 42.536 217.922 41.944 217.954 41.4C218.018 40.824 218.05 40.296 218.05 39.816C218.05 37.768 217.65 36.264 216.85 35.304C216.05 34.344 214.994 33.864 213.682 33.864Z"
            fill="url(#paint0_linear_701_382)"
          />
          <path
            d="M7.11325 78L10 80.8868L12.8868 78L10 75.1132L7.11325 78ZM277.887 78L275 75.1132L272.113 78L275 80.8868L277.887 78ZM10 78.5H275V77.5H10V78.5Z"
            fill="url(#paint1_linear_701_382)"
          />
          <defs>
            <linearGradient
              id="paint0_linear_701_382"
              x1="142.5"
              y1="10"
              x2="142.5"
              y2="68"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#A07A4A" />
              <stop offset="0.33" stop-color="#F4DB7A" />
              <stop offset="0.61" stop-color="#EDCF5D" />
              <stop offset="1" stop-color="#945D25" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_701_382"
              x1="7.5"
              y1="79.9981"
              x2="282.5"
              y2="79.9981"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#A07A4A" />
              <stop offset="0.445" stop-color="#EDCF5D" />
              <stop offset="1" stop-color="#7A5A00" />
            </linearGradient>
          </defs>
        </svg>

        {/* Cards Display - Using SVG cards */}
        <div className="flex justify-between items-start w-full mb-8">
          <div className="w-20 h-32 flex items-start justify-center">
            <img
              src={getCardImagePath('A', 'spades')}
              alt="Ace of Spades"
              className="w-full h-[8vh] object-contain"
            />
          </div>
          <div
            className="relative flex items-start justify-center"
            style={{ width: "100px", height: "128px" }}
          >
            {[0, 1, 2, 3, 4].map((index) => (
              <div
                key={index}
                className="absolute"
                style={{
                  left: `${index * 4}px`,
                  top: 0,
                  zIndex: index,
                  width: "80px",
                  height: "128px",
                }}
              >
                <img
                  src={getCardBackImagePath()}
                  alt="Card Back"
                  className="w-full h-full object-contain"
                />
              </div>
            ))}
          </div>
          <div className="w-20 h-32 flex items-start justify-center">
            <img
              src={getCardImagePath('J', 'clubs')}
              alt="Jack of Clubs"
              className="w-full h-[8vh] object-contain"
            />
          </div>
        </div>

        {/* Server Status Reminder */}
        {/* <div className="w-full mb-4 p-3 bg-yellow-900/30 border border-[#E1C760] rounded-lg">
          <p className="text-sm text-center text-[#E1C760]">
            <strong>Important:</strong> Make sure to start the WebSocket server before creating or joining a lobby.<br />
            Run <code className="bg-black/50 px-2 py-1 rounded">cd server && npm install && npm start</code> in your terminal.
            <br />
            <small>If the server uses a different port, click the <span className="inline-block align-middle"><Settings size={12} /></span> icon in the top-right corner to configure it.</small>
          </p>
        </div> */}

        {/* Player Info */}
        <div className="w-full mb-8">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-lg text-[#E1C760]">Welcome, {playerName}</h2>
            <Button
              variant="outline"
              size="sm"
              className="border-[#E1C760] text-[#E1C760] hover:bg-[#E1C760]/10"
              onClick={() => {        throw new Error(response.Error || 'Failed to create lobby');

                logout();
                navigate('/login');
              }}
            >
              <LogOut className="h-4 w-4 mr-1" />
              Sign Out
            </Button>
          </div>

          {errorMessage && (
            <Alert variant="destructive" className="mt-2 bg-red-900/50 border border-red-500">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-sm">{errorMessage}</AlertDescription>
            </Alert>
          )}
        </div>

        {/* Game Options with custom SVGs */}
        <div className="w-full">
          <LobbyButton
            svgComponent={<HostSVG />}
            buttonText={isLoading ? "Creating..." : "Create Lobby"}
            onClick={handleCreateLobby}
          />

          <LobbyButton
            svgComponent={<PublicSVG />}
            buttonText="Find Game"
            onClick={() => navigate("/game")}
          />

          <LobbyButton
            svgComponent={<PublicSVG />}
            buttonText="Spectate Games"
            onClick={() => navigate("/spectate")}
          />

          <LeaderboardButton onClick={() => navigate("/competitions")} />

          <PrivateSVG />
          <div className="flex justify-end mr-[6vw]">
            <div className="relative w-[160px]">
              <Input
                value={joinCode}
                onChange={(e) => setJoinCode(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter" && joinCode.length > 0) {
                    handleJoinLobby();
                  }
                }}
                placeholder="Enter Code"
                className="text-center bg-transparent border border-[#E1C760] rounded-lg text-[#E1C760] text-lg py-2 px-4 mt-2 w-full h-10 focus-visible:border-[#E1C760] focus-visible:ring-[#E1C760]/50"
                disabled={isLoading}
              />
              {showArrow && (
                <div
                  className="absolute right-3 top-0 bottom-0 flex items-center justify-center cursor-pointer"
                  onClick={handleJoinLobby}
                >
                  <ArrowRight
                    className="text-[#E1C760]"
                    size={18}
                    style={{
                      animation: "fadeInSlide 0.5s ease-out forwards",
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
